package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user")
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 用户信息
     */
    @GetMapping("/info")
    @Operation(summary = "用户信息", description = "用户信息")
    public Result info(){
        return Result.success("用户信息");
    }
    
}
