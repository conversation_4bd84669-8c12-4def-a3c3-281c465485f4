spring:
  application:
    name: subfg-api
  profiles:
    include: repository

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /

# 日志配置
logging:
  level:
    com.subfg: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# SpringDoc配置
springdoc:
  group-configs:
    - group: "system"
      display-name: "系统管理"
      paths-to-match: "/api/health/**"
    - group: "family"
      display-name: "家庭群组管理"
      paths-to-match: "/api/family/**"
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.subfg.subfgapi.controller
  paths-to-match: /api/**
