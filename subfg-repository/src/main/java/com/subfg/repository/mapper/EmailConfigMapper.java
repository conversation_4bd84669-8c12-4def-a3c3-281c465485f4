package com.subfg.repository.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.config.EmailConfig;

/**
 * 邮箱配置 Mapper 接口
 */
@Mapper
public interface EmailConfigMapper extends BaseMapper<EmailConfig> {

    /**
     * 查询邮箱配置（解密后）
     * 从数据库获取邮箱配置并自动解密敏感字段
     *
     * @return 解密后的邮箱配置
     */
    EmailConfig selectEmailConfigDecrypted();

    /**
     * 根据ID查询邮箱配置（解密后）
     *
     * @param id 配置ID
     * @return 解密后的邮箱配置
     */
    EmailConfig selectByIdDecrypted(@Param("id") Integer id);

    /**
     * 插入邮箱配置（加密敏感字段）
     *
     * @param emailConfig 邮箱配置
     * @return 影响行数
     */
    int insertEncrypted(EmailConfig emailConfig);

    /**
     * 更新邮箱配置（加密敏感字段）
     *
     * @param emailConfig 邮箱配置
     * @return 影响行数
     */
    int updateEncrypted(EmailConfig emailConfig);
}
